import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

/// Marathi Linguistic Terms PDF Generator
/// 
/// This class generates a comprehensive PDF explaining Marathi linguistic terms
/// with proper Unicode Devanagari font support for correct conjunct character rendering

class MarathiLinguisticPdfGenerator {
  
  // Helper function to get proper Devanagari font for conjunct characters
  static Future<pw.Font> _getDevanagariFont() async {
    try {
      // Primary: Noto Sans Devanagari - best for conjunct characters
      final font = await PdfGoogleFonts.notoSansDevanagariRegular();
      print('✅ Loaded Noto Sans Devanagari Regular font');
      return font;
    } catch (e1) {
      try {
        // Secondary: Noto Serif Devanagari
        final font = await PdfGoogleFonts.notoSerifDevanagariRegular();
        print('✅ Loaded Noto Serif Devanagari font');
        return font;
      } catch (e2) {
        try {
          // Fallback: Hind font from assets
          final fontData = await rootBundle.load("assets/fonts/Hind-Regular.ttf");
          final font = pw.Font.ttf(fontData);
          print('✅ Loaded Hind font from assets');
          return font;
        } catch (e3) {
          throw Exception('❌ No suitable Devanagari font found. Please ensure internet connectivity.');
        }
      }
    }
  }

  static Future<pw.Font> _getDevanagariBoldFont() async {
    try {
      // Primary: Noto Sans Devanagari Bold
      final font = await PdfGoogleFonts.notoSansDevanagariBold();
      print('✅ Loaded Noto Sans Devanagari Bold font');
      return font;
    } catch (e1) {
      try {
        // Fallback: Regular font for bold
        return await _getDevanagariFont();
      } catch (e2) {
        throw Exception('❌ No suitable bold Devanagari font found');
      }
    }
  }

  // Helper function to ensure proper Devanagari text rendering
  static String _ensureProperDevanagariText(String text) {
    if (text.isEmpty) return text;
    
    // Fix common conjunct characters for PDF rendering
    final Map<String, String> conjunctFixes = {
      'श्री': 'श्री',   // Shri
      'क्ष': 'क्ष',    // Ksha
      'त्र': 'त्र',    // Tra
      'ज्ञ': 'ज्ञ',    // Gya
      'द्व': 'द्व',    // Dva
      'स्व': 'स्व',    // Sva
      'प्र': 'प्र',    // Pra
      'स्थ': 'स्थ',    // Stha
      'न्त': 'न्त',    // Nta
      'न्द': 'न्द',    // Nda
      'स्त': 'स्त',    // Sta
      'न्य': 'न्य',    // Nya
      'त्य': 'त्य',    // Tya
      'द्य': 'द्य',    // Dya
      'म्प': 'म्प',    // Mpa
      'म्ब': 'म्ब',    // Mba
      'च्च': 'च्च',    // Cha
      'त्त': 'त्त',    // Tta
      'ल्ल': 'ल्ल',    // Lla
      'न्न': 'न्न',    // Nna
    };
    
    String processedText = text;
    conjunctFixes.forEach((original, fixed) {
      processedText = processedText.replaceAll(original, fixed);
    });
    
    return processedText.trim();
  }

  // Helper function to create Devanagari text widget
  static pw.Widget _createDevanagariText(
    String text,
    pw.Font font,
    double fontSize, {
    PdfColor? color,
    pw.FontWeight? fontWeight,
    pw.TextAlign? textAlign,
    bool isBold = false,
  }) {
    final processedText = _ensureProperDevanagariText(text);
    
    return pw.Text(
      processedText,
      style: pw.TextStyle(
        font: font,
        fontSize: fontSize,
        color: color ?? PdfColors.black,
        fontWeight: fontWeight ?? (isBold ? pw.FontWeight.bold : pw.FontWeight.normal),
        letterSpacing: 0.0, // No extra spacing for conjuncts
        height: 1.4, // Proper line height for Devanagari
      ),
      textAlign: textAlign ?? pw.TextAlign.left,
      textDirection: pw.TextDirection.ltr,
    );
  }

  /// Generate comprehensive Marathi linguistic terms PDF
  static Future<String?> generateMarathiLinguisticPdf() async {
    try {
      print('🔤 Starting Marathi Linguistic PDF generation...');
      
      // Load fonts
      final regularFont = await _getDevanagariFont();
      final boldFont = await _getDevanagariBoldFont();
      
      final pdf = pw.Document();
      
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(40),
          build: (pw.Context context) {
            return [
              // Title
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex("#FF8C00"), // Orange background
                  borderRadius: pw.BorderRadius.circular(10),
                ),
                child: pw.Column(
                  children: [
                    _createDevanagariText(
                      'मराठी भाषाशास्त्रीय संज्ञा',
                      boldFont,
                      24,
                      color: PdfColors.white,
                      isBold: true,
                      textAlign: pw.TextAlign.center,
                    ),
                    pw.SizedBox(height: 8),
                    _createDevanagariText(
                      'Marathi Linguistic Terms',
                      regularFont,
                      16,
                      color: PdfColors.white,
                      textAlign: pw.TextAlign.center,
                    ),
                  ],
                ),
              ),
              
              pw.SizedBox(height: 30),
              
              // Section 1: जोडशब्द (Compound Words)
              _buildSection(
                title: '१. जोडशब्द (Compound Words)',
                content: [
                  'जोडशब्द म्हणजे दोन किंवा अधिक शब्दांना जोडून तयार केलेले नवीन शब्द. हे शब्द एकत्र येऊन नवीन अर्थ निर्माण करतात.',
                  '',
                  'उदाहरणे:',
                  '• गृहकार्य = गृह + कार्य (घरचे काम)',
                  '• विद्यालय = विद्या + आलय (शिक्षणाचे ठिकाण)',
                  '• सूर्यप्रकाश = सूर्य + प्रकाश (सूर्याचा प्रकाश)',
                ],
                regularFont: regularFont,
                boldFont: boldFont,
              ),
              
              pw.SizedBox(height: 25),
              
              // Section 2: वेलांटी (Virama/Halant)
              _buildSection(
                title: '२. वेलांटी/हलंत (Virama/Halant)',
                content: [
                  'वेलांटी (्) हे एक विशेष चिन्ह आहे जे व्यंजनाच्या खाली लावले जाते. हे व्यंजनातील अंतर्निहित स्वर काढून टाकते आणि दोन व्यंजनांना जोडते.',
                  '',
                  'उदाहरणे:',
                  '• क् + ष = क्ष (जसे: क्षत्रिय)',
                  '• त् + र = त्र (जसे: त्रिकोण)',
                  '',
                  'वेलांटीमुळे जोडशब्द तयार होतात आणि उच्चार बदलतो.',
                ],
                regularFont: regularFont,
                boldFont: boldFont,
              ),
              
              pw.SizedBox(height: 25),
              
              // Section 3: उकार (Ukar)
              _buildSection(
                title: '३. उकार (Ukar)',
                content: [
                  'उकार (ु) हा एक मात्रा आहे जो व्यंजनाच्या खाली लावला जातो. हा "उ" स्वराचा चिन्ह आहे.',
                  '',
                  'उदाहरणे:',
                  '• क + ु = कु (जसे: कुत्रा)',
                  '• ग + ु = गु (जसे: गुरु)',
                  '',
                  'उकार लावल्यामुळे व्यंजनाचा उच्चार "उ" स्वरासह होतो.',
                ],
                regularFont: regularFont,
                boldFont: boldFont,
              ),
              
              pw.SizedBox(height: 25),
              
              // Section 4: अवगड शब्द (Difficult Words)
              _buildSection(
                title: '४. अवगड शब्द (Difficult Words)',
                content: [
                  'अवगड शब्द म्हणजे असे शब्द ज्यांचा उच्चार किंवा लेखन कठीण असते. यामध्ये संस्कृत मूळ शब्द, जोडशब्द आणि तत्सम शब्दांचा समावेश होतो.',
                  '',
                  'उदाहरणे:',
                  '• क्षत्रिय = योद्धा वर्ग (संस्कृत मूळ शब्द)',
                  '• ज्ञानेश्वर = ज्ञानाचा ईश्वर (संत नाम)',
                  '• स्वातंत्र्य = स्वतंत्रता (तत्सम शब्द)',
                ],
                regularFont: regularFont,
                boldFont: boldFont,
              ),
              
              pw.SizedBox(height: 30),
              
              // Footer
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(15),
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex("#F0F8FF"),
                  borderRadius: pw.BorderRadius.circular(8),
                  border: pw.Border.all(color: PdfColor.fromHex("#4169E1")),
                ),
                child: pw.Column(
                  children: [
                    _createDevanagariText(
                      'टीप: हे सर्व उदाहरणे Unicode Devanagari font वापरून तयार केली आहेत.',
                      regularFont,
                      12,
                      color: PdfColor.fromHex("#4169E1"),
                      textAlign: pw.TextAlign.center,
                    ),
                    pw.SizedBox(height: 5),
                    pw.Text(
                      'Generated with proper conjunct character support',
                      style: pw.TextStyle(
                        font: regularFont,
                        fontSize: 10,
                        color: PdfColor.fromHex("#666666"),
                      ),
                      textAlign: pw.TextAlign.center,
                    ),
                  ],
                ),
              ),
            ];
          },
        ),
      );
      
      // Save PDF
      if (kIsWeb) {
        await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdf.save());
        return null;
      } else {
        final output = await getTemporaryDirectory();
        final file = File("${output.path}/marathi_linguistic_terms.pdf");
        await file.writeAsBytes(await pdf.save());
        print('✅ PDF generated successfully: ${file.path}');
        return file.path;
      }
      
    } catch (e) {
      print('❌ Error generating PDF: $e');
      throw Exception('Failed to generate Marathi linguistic PDF: $e');
    }
  }

  // Helper method to build sections
  static pw.Widget _buildSection({
    required String title,
    required List<String> content,
    required pw.Font regularFont,
    required pw.Font boldFont,
  }) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Section title
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          decoration: pw.BoxDecoration(
            color: PdfColor.fromHex("#E6F3FF"),
            borderRadius: pw.BorderRadius.circular(5),
            border: pw.Border.all(color: PdfColor.fromHex("#4169E1")),
          ),
          child: _createDevanagariText(
            title,
            boldFont,
            16,
            color: PdfColor.fromHex("#4169E1"),
            isBold: true,
          ),
        ),
        
        pw.SizedBox(height: 15),
        
        // Section content
        ...content.map((text) {
          if (text.isEmpty) {
            return pw.SizedBox(height: 8);
          } else if (text.startsWith('•')) {
            return pw.Padding(
              padding: const pw.EdgeInsets.only(left: 20, bottom: 5),
              child: _createDevanagariText(text, regularFont, 12),
            );
          } else if (text.contains('उदाहरणे:')) {
            return pw.Padding(
              padding: const pw.EdgeInsets.only(top: 5, bottom: 5),
              child: _createDevanagariText(text, boldFont, 13, isBold: true),
            );
          } else {
            return pw.Padding(
              padding: const pw.EdgeInsets.only(bottom: 8),
              child: _createDevanagariText(text, regularFont, 12),
            );
          }
        }),
      ],
    );
  }
}

/// Flutter Widget to generate and display Marathi Linguistic PDF
class MarathiLinguisticScreen extends StatefulWidget {
  const MarathiLinguisticScreen({super.key});

  @override
  State<MarathiLinguisticScreen> createState() => _MarathiLinguisticScreenState();
}

class _MarathiLinguisticScreenState extends State<MarathiLinguisticScreen> {
  bool _isGenerating = false;
  String _status = 'Ready to generate Marathi linguistic terms PDF';

  Future<void> _generatePdf() async {
    setState(() {
      _isGenerating = true;
      _status = 'Generating PDF with proper Devanagari fonts...';
    });

    try {
      final pdfPath = await MarathiLinguisticPdfGenerator.generateMarathiLinguisticPdf();

      setState(() {
        _status = pdfPath != null
            ? '✅ PDF generated successfully!\nPath: $pdfPath'
            : '✅ PDF opened in browser (Web platform)';
        _isGenerating = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Marathi Linguistic PDF generated successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }

    } catch (e) {
      setState(() {
        _status = '❌ Error generating PDF: $e';
        _isGenerating = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('मराठी भाषाशास्त्रीय संज्ञा'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.orange.shade400, Colors.orange.shade600],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Column(
                children: [
                  Text(
                    'मराठी भाषाशास्त्रीय संज्ञा',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Marathi Linguistic Terms PDF Generator',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 30),

            // Content preview
            const Text(
              'PDF मध्ये समाविष्ट विषय:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: 15),

            // Topics list
            _buildTopicCard('१. जोडशब्द (Compound Words)',
                'दोन किंवा अधिक शब्दांना जोडून तयार केलेले शब्द'),
            _buildTopicCard('२. वेलांटी/हलंत (Virama/Halant)',
                'व्यंजनाच्या खाली लावले जाणारे विशेष चिन्ह (्)'),
            _buildTopicCard('३. उकार (Ukar)',
                'व्यंजनाच्या खाली लावली जाणारी "उ" स्वराची मात्रा (ु)'),
            _buildTopicCard('४. अवगड शब्द (Difficult Words)',
                'उच्चार किंवा लेखन कठीण असणारे शब्द'),

            const SizedBox(height: 30),

            // Status
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Text(
                _status,
                style: const TextStyle(fontSize: 14),
              ),
            ),

            const Spacer(),

            // Generate button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isGenerating ? null : _generatePdf,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  padding: const EdgeInsets.all(16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isGenerating
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(color: Colors.white),
                          ),
                          SizedBox(width: 10),
                          Text('PDF तयार करत आहे...',
                              style: TextStyle(fontSize: 16, color: Colors.white)),
                        ],
                      )
                    : const Text(
                        'मराठी भाषाशास्त्रीय PDF तयार करा',
                        style: TextStyle(fontSize: 16, color: Colors.white),
                      ),
              ),
            ),

            const SizedBox(height: 20),

            // Note
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.amber.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.amber.shade200),
              ),
              child: const Text(
                '📝 टीप: हा PDF Unicode Devanagari font वापरून तयार केला जातो, त्यामुळे सर्व जोडशब्द आणि मराठी अक्षरे योग्यरित्या दिसतील.',
                style: TextStyle(fontSize: 12, color: Colors.amber),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopicCard(String title, String description) {
    return Card(
      margin: const EdgeInsets.only(bottom: 10),
      child: Padding(
        padding: const EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 5),
            Text(
              description,
              style: const TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
