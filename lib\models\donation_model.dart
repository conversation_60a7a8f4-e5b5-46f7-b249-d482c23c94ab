import 'package:hive/hive.dart';

part 'donation_model.g.dart';

@HiveType(typeId: 3)
class Donation extends HiveObject {
  @HiveField(0)
  String donorName;

  @HiveField(1)
  double amount;

  @HiveField(2)
  String? reason;

  @HiveField(3)
  DateTime date;

  @HiveField(4) // New field for donation number
  int donationNo;

  Donation({
    required this.donorName,
    required this.amount,
    this.reason,
    required this.date,
    required this.donationNo,
  });
}
