// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'donation_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class DonationAdapter extends TypeAdapter<Donation> {
  @override
  final int typeId = 3;

  @override
  Donation read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Donation(
      donorName: fields[0] as String,
      amount: fields[1] as double,
      reason: fields[2] as String?,
      date: fields[3] as DateTime,
      donationNo: fields[4] as int,
    );
  }

  @override
  void write(BinaryWriter writer, Donation obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.donorName)
      ..writeByte(1)
      ..write(obj.amount)
      ..writeByte(2)
      ..write(obj.reason)
      ..writeByte(3)
      ..write(obj.date)
      ..writeByte(4)
      ..write(obj.donationNo);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DonationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
