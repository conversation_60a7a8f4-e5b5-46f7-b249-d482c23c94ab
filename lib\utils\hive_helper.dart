import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:wargani/models/profile_model.dart';
import 'package:wargani/models/wargani_model.dart';
import 'package:wargani/models/expense_model.dart';
import 'package:wargani/models/donation_model.dart';
import 'package:wargani/models/user_model.dart';

class HiveHelper {
  static Future<void> init() async {
    if (!kIsWeb) {
      final appDocumentDir = await getApplicationDocumentsDirectory();
      await Hive.initFlutter(appDocumentDir.path);
    } else {
      await Hive.initFlutter();
    }

    Hive.registerAdapter(ProfileAdapter());
    Hive.registerAdapter(WarganiAdapter());
    Hive.registerAdapter(ExpenseAdapter());
    Hive.registerAdapter(DonationAdapter());
    Hive.registerAdapter(UserAdapter());

    await Hive.openBox<Profile>('profile');
    await Hive.openBox<Wargani>('wargani');
    await Hive.openBox<Expense>('expenses');
    await Hive.openBox<Donation>('donations');
    await Hive.openBox<User>('users');
    await Hive.openBox<String>('settings'); // Open a new box for settings
    await Hive.openBox<Wargani>('deleted_wargani'); // Open bin folder for deleted receipts
    await Hive.openBox('deleted_donations'); // Open bin folder for deleted donations
    await Hive.openBox('deleted_expenses'); // Open bin folder for deleted expenses
  }

  static Box<Profile> getProfileBox() => Hive.box<Profile>('profile');
  static Box<User> getUsersBox() => Hive.box<User>('users');
  static Box<Wargani> getWarganiBox() => Hive.box<Wargani>('wargani');
  static Box<Expense> getExpensesBox() => Hive.box<Expense>('expenses');
  static Box<Donation> getDonationsBox() => Hive.box<Donation>('donations');
  static Box<String> getSettingsBox() => Hive.box<String>('settings'); // New method to get settings box
  static Box<Wargani> getDeletedWarganiBox() => Hive.box<Wargani>('deleted_wargani'); // Bin folder for deleted receipts
  static Box getDeletedDonationsBox() => Hive.box('deleted_donations'); // Bin folder for deleted donations
  static Box getDeletedExpensesBox() => Hive.box('deleted_expenses'); // Bin folder for deleted expenses

  static Future<void> saveLocale(String localeCode) async {
    await getSettingsBox().put('locale', localeCode);
  }

  static String? getLocale() {
    return getSettingsBox().get('locale');
  }

  // Helper method to get next donation number
  static int getNextDonationNumber() {
    final donationsBox = getDonationsBox();
    return donationsBox.length + 1;
  }

  static Future<void> clearData() async {
    await getProfileBox().clear();
    await getUsersBox().clear();
    await getWarganiBox().clear();
    await getExpensesBox().clear();
    await getDonationsBox().clear();
    await getSettingsBox().clear();
  }
}
