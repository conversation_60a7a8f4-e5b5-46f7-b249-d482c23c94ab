import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wargani/screens/login_screen.dart';

class PrivacyPolicyScreen extends StatefulWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  State<PrivacyPolicyScreen> createState() => _PrivacyPolicyScreenState();
}

class _PrivacyPolicyScreenState extends State<PrivacyPolicyScreen> {
  bool _privacyPolicyAccepted = false;
  bool _storagePermissionAccepted = false;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
              // App Logo and Title - Compact
              Center(
                child: Column(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(15),
                        child: Image.asset(
                          'assets/logo.png',
                          width: 50,
                          height: 50,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            // Fallback to icon if image fails to load
                            return const Icon(
                              Icons.temple_hindu,
                              color: Colors.white,
                              size: 30,
                            );
                          },
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Mandal Management Application',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(height: 2),
                    const Text(
                      'Welcome to Mandal Management System',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Privacy Policy Section
              const Text(
                'Privacy Policy & Terms',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),

              const SizedBox(height: 12),
              
              // Compact Privacy Policy Summary
              Container(
                padding: const EdgeInsets.all(14),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Quick Summary:',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '• Your data stays on your device only\n'
                      '• We need storage permission for saving receipts\n'
                      '• No data is shared with third parties\n'
                      '• You have full control over your information',
                      style: TextStyle(
                        fontSize: 12,
                        height: 1.4,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Center(
                      child: TextButton.icon(
                        onPressed: _showFullTerms,
                        icon: const Icon(Icons.article_outlined, size: 16),
                        label: const Text(
                          'Read Full Terms & Privacy Policy',
                          style: TextStyle(fontSize: 12),
                        ),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.orange,
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 14),

              // Checkboxes - Compact
              _buildCheckboxTile(
                title: 'I accept the Privacy Policy and Terms of Service',
                subtitle: 'Required to use the app',
                value: _privacyPolicyAccepted,
                onChanged: (value) {
                  setState(() {
                    _privacyPolicyAccepted = value ?? false;
                  });
                },
              ),

              const SizedBox(height: 8),

              _buildCheckboxTile(
                title: kIsWeb ? 'Grant App Permissions' : 'Grant Storage Permission',
                subtitle: kIsWeb
                    ? 'Required for app functionality'
                    : 'Required to save receipts and create backups',
                value: _storagePermissionAccepted,
                onChanged: (value) {
                  setState(() {
                    _storagePermissionAccepted = value ?? false;
                  });
                },
              ),

              const SizedBox(height: 16),
              
              // Accept Button - Extra Compact
              SizedBox(
                width: double.infinity,
                height: 44,
                child: ElevatedButton(
                  onPressed: (_privacyPolicyAccepted && _storagePermissionAccepted && !_isLoading)
                      ? _handleAccept
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 1,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 18,
                          height: 18,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : const Text(
                          'Accept and Continue',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),

              const SizedBox(height: 8),

              // Not Accept Button - Extra Compact
              SizedBox(
                width: double.infinity,
                height: 36,
                child: TextButton(
                  onPressed: _isLoading ? null : _handleNotAccept,
                  child: const Text(
                    'Not Accept (Exit App)',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.red,
                    ),
                  ),
                ),
              ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCheckboxTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool?> onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: value ? Colors.orange : Colors.grey[300]!,
          width: value ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          Transform.scale(
            scale: 0.8,
            child: Checkbox(
              value: value,
              onChanged: onChanged,
              activeColor: Colors.orange,
            ),
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 1),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleAccept() async {
    setState(() {
      _isLoading = true;
    });

    try {
      bool permissionGranted = true;

      // Request storage permission only on mobile platforms
      if (!kIsWeb) {
        // Request multiple permissions for better compatibility
        Map<Permission, PermissionStatus> permissions = await [
          Permission.storage,
          Permission.manageExternalStorage,
          Permission.photos,
        ].request();

        // Check if any storage permission is granted
        permissionGranted = permissions[Permission.storage]?.isGranted == true ||
            permissions[Permission.storage]?.isLimited == true ||
            permissions[Permission.manageExternalStorage]?.isGranted == true ||
            permissions[Permission.photos]?.isGranted == true;

        // If no permission granted, try alternative approach
        if (!permissionGranted) {
          // For Android 13+ (API 33+), try media permissions
          Map<Permission, PermissionStatus> mediaPermissions = await [
            Permission.photos,
            Permission.videos,
            Permission.audio,
          ].request();

          permissionGranted = mediaPermissions.values.any((status) =>
              status.isGranted || status.isLimited);
        }
      }
      // On web, we don't need storage permission, so we consider it granted

      if (permissionGranted || kIsWeb) {
        // Save that user has accepted privacy policy
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('privacy_policy_accepted', true);
        await prefs.setBool('first_launch_completed', true);

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ Welcome to Mandal Management! Setup completed successfully.'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }

        // Wait a moment for user to see the success message
        await Future.delayed(const Duration(milliseconds: 1500));

        // Navigate to login screen
        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => const LoginScreen(),
            ),
          );
        }
      } else {
        // Show dialog with options if storage permission denied (only on mobile)
        if (mounted) {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: const Text('⚠️ Permission Required'),
                content: const Text(
                  'Storage permission is needed to save receipts and data. You can:\n\n'
                  '1. Continue without permission (limited functionality)\n'
                  '2. Grant permission in app settings\n'
                  '3. Try again',
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      // Continue without permission
                      _continueWithoutPermission();
                    },
                    child: const Text('Continue Anyway'),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      // Try again
                      _handleAccept();
                    },
                    child: const Text('Try Again'),
                  ),
                ],
              );
            },
          );
        }
      }
    } catch (e) {
      debugPrint('Error in _handleAccept: $e'); // Debug log
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _continueWithoutPermission() async {
    try {
      // Save that user has accepted privacy policy
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('privacy_policy_accepted', true);
      await prefs.setBool('first_launch_completed', true);
      await prefs.setBool('storage_permission_denied', true); // Track that permission was denied

      // Show warning message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('⚠️ Continuing with limited functionality. Some features may not work properly.'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 3),
          ),
        );
      }

      // Wait a moment for user to see the message
      await Future.delayed(const Duration(milliseconds: 2000));

      // Navigate to login screen
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const LoginScreen(),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error in _continueWithoutPermission: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showFullTerms() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Privacy Policy & Terms of Service',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          content: const SizedBox(
            width: double.maxFinite,
            height: 400,
            child: SingleChildScrollView(
              child: Text(
                '''Welcome to AMSSoftX Mandal Management Application!

By using this application, you agree to the following terms and conditions:

DATA COLLECTION & PRIVACY:
• We collect and store your personal information (name, email, phone) locally on your device
• Your donation and expense records are stored securely on your device
• We do not share your personal data with third parties
• All data remains on your device and is not transmitted to external servers

STORAGE PERMISSIONS:
• This app requires storage permission to save receipts, reports, and backup data
• Storage access is used only for app functionality (saving PDFs, creating backups)
• We do not access or modify other files on your device

DATA SECURITY:
• Your data is encrypted and stored locally using secure storage methods
• Regular backups help protect your data from loss
• You have full control over your data and can delete it anytime

USAGE TERMS:
• This app is designed for managing Ganesh Mandal donations and expenses
• Users are responsible for the accuracy of entered data
• The app is provided "as is" without warranties

PERMISSIONS REQUIRED:
• Storage: To save receipts, reports, and backup files
• Camera (optional): To capture receipt photos
• Files access: To export and share reports

DATA RETENTION:
• Data is stored locally until you delete the app
• You can clear all data from app settings
• No automatic data transmission to external servers

CONTACT:
For any questions or concerns about privacy, please contact our support team.

Devloped By AMSSoftX All Rights Reserved.
Web: https://amssoftx.com/
Last updated: july 2025''',
                style: TextStyle(
                  fontSize: 14,
                  height: 1.5,
                  color: Colors.black87,
                ),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Close',
                style: TextStyle(color: Colors.orange),
              ),
            ),
          ],
        );
      },
    );
  }

  void _handleNotAccept() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Exit App'),
          content: const Text(
            'You must accept the privacy policy and grant storage permission to use this app. Do you want to exit?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Exit the app
                Navigator.of(context).pop();
              },
              child: const Text(
                'Exit',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}
