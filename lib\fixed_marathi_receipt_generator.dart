import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:intl/intl.dart';

/// Fixed Marathi Receipt PDF Generator with hardcoded labels
/// 
/// This class generates Marathi receipts with FIXED labels that never change:
/// - || श्री गणेश प्रसन्न ||
/// - गणेशोत्सव
/// - पावती नं.
/// - रोख मिळाले:
/// - अक्षरी:
/// - दिनांक:
/// - यांच्याकडून
/// - धन्यवाद...!
/// - सही
/// - Developed by AMSSoftX Web: https://amsssoftx.com
/// - रोख मिळाले...!
/// 
/// Only dynamic fields: amount, date, donor name, amount in words

class FixedMarathiReceiptGenerator {
  
  // HARDCODED LABELS - NEVER CHANGE THESE
  static const String FIXED_TITLE = '|| श्री गणेश प्रसन्न ||';
  static const String FIXED_FESTIVAL = 'गणेशोत्सव';
  static const String FIXED_RECEIPT_NO = 'पावती नं.';
  static const String FIXED_CASH_RECEIVED = 'रोख मिळाले:';
  static const String FIXED_IN_WORDS = 'अक्षरी:';
  static const String FIXED_DATE = 'दिनांक:';
  static const String FIXED_FROM_SUFFIX = 'यांच्याकडून';
  static const String FIXED_THANK_YOU = 'धन्यवाद...!';
  static const String FIXED_SIGNATURE = 'सही';
  static const String FIXED_DEVELOPER = 'Developed by AMSSoftX Web: https://amsssoftx.com';
  static const String FIXED_CASH_RECEIVED_FOOTER = 'रोख मिळाले...!';
  
  // Helper function to get proper Devanagari font
  static Future<pw.Font> _getDevanagariFont() async {
    try {
      // Primary: Noto Sans Devanagari - best for conjunct characters
      final font = await PdfGoogleFonts.notoSansDevanagariRegular();
      print('✅ Loaded Noto Sans Devanagari Regular font');
      return font;
    } catch (e1) {
      try {
        // Secondary: Noto Serif Devanagari
        final font = await PdfGoogleFonts.notoSerifDevanagariRegular();
        print('✅ Loaded Noto Serif Devanagari font');
        return font;
      } catch (e2) {
        try {
          // Fallback: Hind font from assets
          final fontData = await rootBundle.load("assets/fonts/Hind-Regular.ttf");
          final font = pw.Font.ttf(fontData);
          print('✅ Loaded Hind font from assets');
          return font;
        } catch (e3) {
          throw Exception('❌ No suitable Devanagari font found. Please ensure internet connectivity.');
        }
      }
    }
  }

  static Future<pw.Font> _getDevanagariBoldFont() async {
    try {
      // Primary: Noto Sans Devanagari Bold
      final font = await PdfGoogleFonts.notoSansDevanagariBold();
      print('✅ Loaded Noto Sans Devanagari Bold font');
      return font;
    } catch (e1) {
      try {
        // Fallback: Regular font for bold
        return await _getDevanagariFont();
      } catch (e2) {
        throw Exception('❌ No suitable bold Devanagari font found');
      }
    }
  }

  // Helper function to ensure proper Devanagari text rendering
  static String _ensureProperDevanagariText(String text) {
    if (text.isEmpty) return text;
    
    // Fix common conjunct characters for PDF rendering
    final Map<String, String> conjunctFixes = {
      'श्री': 'श्री',   // Shri
      'क्ष': 'क्ष',    // Ksha
      'त्र': 'त्र',    // Tra
      'ज्ञ': 'ज्ञ',    // Gya
      'द्व': 'द्व',    // Dva
      'स्व': 'स्व',    // Sva
      'प्र': 'प्र',    // Pra
      'स्थ': 'स्थ',    // Stha
      'न्त': 'न्त',    // Nta
      'न्द': 'न्द',    // Nda
      'स्त': 'स्त',    // Sta
      'न्य': 'न्य',    // Nya
      'त्य': 'त्य',    // Tya
      'द्य': 'द्य',    // Dya
      'म्प': 'म्प',    // Mpa
      'म्ब': 'म्ब',    // Mba
      'च्च': 'च्च',    // Cha
      'त्त': 'त्त',    // Tta
      'ल्ल': 'ल्ल',    // Lla
      'न्न': 'न्न',    // Nna
      'पू': 'पू',      // Poo
      'जन': 'जन',     // Jan
      'सन': 'सन',     // San
      'प्रसन': 'प्रसन', // Prasan
    };
    
    String processedText = text;
    conjunctFixes.forEach((original, fixed) {
      processedText = processedText.replaceAll(original, fixed);
    });
    
    return processedText.trim();
  }

  // Helper function to create Devanagari text widget
  static pw.Widget _createDevanagariText(
    String text,
    pw.Font font,
    double fontSize, {
    PdfColor? color,
    pw.FontWeight? fontWeight,
    pw.TextAlign? textAlign,
    bool isBold = false,
  }) {
    final processedText = _ensureProperDevanagariText(text);
    
    return pw.Text(
      processedText,
      style: pw.TextStyle(
        font: font,
        fontSize: fontSize,
        color: color ?? PdfColors.black,
        fontWeight: fontWeight ?? (isBold ? pw.FontWeight.bold : pw.FontWeight.normal),
        letterSpacing: 0.0, // No extra spacing for conjuncts
        height: 1.4, // Proper line height for Devanagari
      ),
      textAlign: textAlign ?? pw.TextAlign.left,
      textDirection: pw.TextDirection.ltr,
    );
  }

  // Convert number to Marathi words
  static String _convertToMarathiWords(double amount) {
    final int intAmount = amount.toInt();
    
    final Map<int, String> marathiNumbers = {
      0: 'शून्य', 1: 'एक', 2: 'दोन', 3: 'तीन', 4: 'चार', 5: 'पाच',
      6: 'सहा', 7: 'सात', 8: 'आठ', 9: 'नऊ', 10: 'दहा',
      11: 'अकरा', 12: 'बारा', 13: 'तेरा', 14: 'चौदा', 15: 'पंधरा',
      16: 'सोळा', 17: 'सतरा', 18: 'अठरा', 19: 'एकोणीस', 20: 'वीस',
      21: 'एकवीस', 22: 'बावीस', 23: 'तेवीस', 24: 'चोवीस', 25: 'पंचवीस',
      30: 'तीस', 40: 'चाळीस', 50: 'पन्नास', 60: 'साठ', 70: 'सत्तर',
      80: 'ऐंशी', 90: 'नव्वद', 100: 'शंभर'
    };
    
    if (intAmount == 0) return 'शून्य रुपये फक्त';
    
    String result = '';
    
    // Handle thousands
    if (intAmount >= 1000) {
      final thousands = intAmount ~/ 1000;
      if (thousands == 1) {
        result += 'एक हजार ';
      } else if (thousands <= 25 && marathiNumbers.containsKey(thousands)) {
        result += '${marathiNumbers[thousands]} हजार ';
      } else {
        result += '$thousands हजार ';
      }
    }
    
    // Handle hundreds
    final remainder = intAmount % 1000;
    if (remainder >= 100) {
      final hundreds = remainder ~/ 100;
      if (hundreds == 1) {
        result += 'एकशे ';
      } else if (marathiNumbers.containsKey(hundreds)) {
        result += '${marathiNumbers[hundreds]}शे ';
      } else {
        result += '${marathiNumbers[hundreds]}शे ';
      }
    }
    
    // Handle tens and units
    final tensAndUnits = remainder % 100;
    if (tensAndUnits > 0) {
      if (marathiNumbers.containsKey(tensAndUnits)) {
        result += marathiNumbers[tensAndUnits]!;
      } else if (tensAndUnits > 25) {
        final tens = tensAndUnits ~/ 10;
        final units = tensAndUnits % 10;
        
        if (tens == 3) {
          result += 'तीस';
        } else if (tens == 4) result += 'चाळीस';
        else if (tens == 5) result += 'पन्नास';
        else if (tens == 6) result += 'साठ';
        else if (tens == 7) result += 'सत्तर';
        else if (tens == 8) result += 'ऐंशी';
        else if (tens == 9) result += 'नव्वद';
        
        if (units > 0 && marathiNumbers.containsKey(units)) {
          result += marathiNumbers[units]!;
        }
      } else {
        result += tensAndUnits.toString();
      }
    }
    
    result += ' रुपये फक्त';
    return result.trim();
  }

  /// Generate Fixed Marathi receipt PDF with hardcoded labels
  /// 
  /// Dynamic parameters:
  /// - [amount]: Amount in rupees (e.g., 4425.0)
  /// - [date]: Receipt date
  /// - [donorName]: Name of the donor
  /// - [receiptNumber]: Receipt number
  /// - [currentYear]: Year for festival (e.g., "2025")
  static Future<String?> generateFixedMarathiReceipt({
    required double amount,
    required DateTime date,
    required String donorName,
    required int receiptNumber,
    required String currentYear,
  }) async {
    try {
      print('🧾 Starting Fixed Marathi receipt PDF generation...');
      
      // Load fonts
      final regularFont = await _getDevanagariFont();
      final boldFont = await _getDevanagariBoldFont();
      
      final pdf = pw.Document();
      
      // Convert amount to Marathi words
      final amountInWords = _convertToMarathiWords(amount);
      
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(30),
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header with FIXED title
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(20),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex("#FF8C00"), // Orange background
                    borderRadius: pw.BorderRadius.circular(10),
                  ),
                  child: pw.Column(
                    children: [
                      // FIXED TITLE - NEVER CHANGE
                      _createDevanagariText(
                        FIXED_TITLE, // || श्री गणेश प्रसन्न ||
                        boldFont,
                        20,
                        color: PdfColors.white,
                        isBold: true,
                        textAlign: pw.TextAlign.center,
                      ),
                      pw.SizedBox(height: 8),
                      // FIXED FESTIVAL WITH DYNAMIC YEAR
                      _createDevanagariText(
                        '$FIXED_FESTIVAL $currentYear', // गणेशोत्सव 2025
                        regularFont,
                        14,
                        color: PdfColors.white,
                        textAlign: pw.TextAlign.center,
                      ),
                    ],
                  ),
                ),
                
                pw.SizedBox(height: 30),
                
                // Receipt details with FIXED labels
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    // FIXED RECEIPT NUMBER LABEL
                    _createDevanagariText(
                      '$FIXED_RECEIPT_NO $receiptNumber', // पावती नं. 1001
                      boldFont,
                      16,
                      isBold: true,
                    ),
                    // FIXED DATE LABEL
                    _createDevanagariText(
                      '$FIXED_DATE ${DateFormat('dd/MM/yyyy').format(date)}', // दिनांक: 18/07/2025
                      regularFont,
                      14,
                    ),
                  ],
                ),
                
                pw.SizedBox(height: 25),
                
                // Donor name with FIXED prefix and suffix
                pw.Row(
                  children: [
                    _createDevanagariText(
                      'सो.श्री ', // FIXED PREFIX
                      regularFont,
                      14,
                    ),
                    pw.Expanded(
                      child: pw.Container(
                        decoration: const pw.BoxDecoration(
                          border: pw.Border(
                            bottom: pw.BorderSide(color: PdfColors.black),
                          ),
                        ),
                        child: pw.Text(
                          donorName, // DYNAMIC: Donor name
                          style: pw.TextStyle(
                            font: boldFont,
                            fontSize: 14,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    // FIXED SUFFIX
                    _createDevanagariText(
                      ' $FIXED_FROM_SUFFIX', // यांच्याकडून
                      regularFont,
                      14,
                    ),
                  ],
                ),
                
                pw.SizedBox(height: 25),
                
                // Amount section with FIXED label
                pw.Row(
                  children: [
                    // FIXED CASH RECEIVED LABEL
                    _createDevanagariText(
                      '$FIXED_CASH_RECEIVED ', // रोख मिळाले:
                      regularFont,
                      14,
                    ),
                    pw.Container(
                      padding: const pw.EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                      decoration: pw.BoxDecoration(
                        border: pw.Border.all(color: PdfColors.black),
                        borderRadius: pw.BorderRadius.circular(5),
                      ),
                      child: pw.Text(
                        '₹ ${amount.toStringAsFixed(0)}', // DYNAMIC: Amount
                        style: pw.TextStyle(
                          font: boldFont,
                          fontSize: 16,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                
                pw.SizedBox(height: 20),
                
                // Amount in words with FIXED label
                pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    // FIXED IN WORDS LABEL
                    _createDevanagariText(
                      '$FIXED_IN_WORDS ', // अक्षरी:
                      regularFont,
                      12,
                    ),
                    pw.Expanded(
                      child: pw.Container(
                        decoration: const pw.BoxDecoration(
                          border: pw.Border(
                            bottom: pw.BorderSide(color: PdfColors.black),
                          ),
                        ),
                        child: _createDevanagariText(
                          amountInWords, // DYNAMIC: Amount in Marathi words
                          regularFont,
                          12,
                        ),
                      ),
                    ),
                  ],
                ),
                
                pw.Spacer(),
                
                // Footer with FIXED labels
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        // FIXED THANK YOU
                        _createDevanagariText(
                          FIXED_THANK_YOU, // धन्यवाद...!
                          boldFont,
                          14,
                          isBold: true,
                        ),
                        pw.SizedBox(height: 15),
                        // FIXED SIGNATURE
                        _createDevanagariText(
                          FIXED_SIGNATURE, // सही
                          regularFont,
                          12,
                        ),
                      ],
                    ),
                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.end,
                      children: [
                        // FIXED CASH RECEIVED FOOTER
                        _createDevanagariText(
                          FIXED_CASH_RECEIVED_FOOTER, // रोख मिळाले...!
                          boldFont,
                          14,
                          isBold: true,
                        ),
                      ],
                    ),
                  ],
                ),
                
                pw.SizedBox(height: 20),
                
                // FIXED DEVELOPER CREDIT
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex("#F0F8FF"),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Text(
                    FIXED_DEVELOPER, // Developed by AMSSoftX Web: https://amsssoftx.com
                    style: pw.TextStyle(
                      font: regularFont,
                      fontSize: 8,
                      color: PdfColor.fromHex("#666666"),
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              ],
            );
          },
        ),
      );
      
      // Save PDF
      if (kIsWeb) {
        await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdf.save());
        return null;
      } else {
        final output = await getTemporaryDirectory();
        final file = File("${output.path}/fixed_marathi_receipt_${receiptNumber}_${DateTime.now().millisecondsSinceEpoch}.pdf");
        await file.writeAsBytes(await pdf.save());
        print('✅ Fixed Marathi receipt PDF generated: ${file.path}');
        return file.path;
      }
      
    } catch (e) {
      print('❌ Error generating Fixed Marathi receipt PDF: $e');
      throw Exception('Failed to generate Fixed Marathi receipt: $e');
    }
  }
}

/// Flutter Widget for Fixed Marathi Receipt Generator
class FixedMarathiReceiptScreen extends StatefulWidget {
  const FixedMarathiReceiptScreen({super.key});

  @override
  State<FixedMarathiReceiptScreen> createState() => _FixedMarathiReceiptScreenState();
}

class _FixedMarathiReceiptScreenState extends State<FixedMarathiReceiptScreen> {
  final _formKey = GlobalKey<FormState>();

  // Form controllers for DYNAMIC fields only
  final _receiptNumberController = TextEditingController(text: '1001');
  final _donorNameController = TextEditingController(text: 'राम शर्मा');
  final _amountController = TextEditingController(text: '4425');
  final _currentYearController = TextEditingController(text: '2025');

  DateTime _selectedDate = DateTime.now();
  bool _isGenerating = false;
  String _status = 'Ready to generate Fixed Marathi receipt';

  @override
  void dispose() {
    _receiptNumberController.dispose();
    _donorNameController.dispose();
    _amountController.dispose();
    _currentYearController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _generateFixedReceipt() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isGenerating = true;
      _status = 'Generating Fixed Marathi receipt with hardcoded labels...';
    });

    try {
      final amount = double.parse(_amountController.text);
      final receiptNumber = int.parse(_receiptNumberController.text);

      final pdfPath = await FixedMarathiReceiptGenerator.generateFixedMarathiReceipt(
        amount: amount,
        date: _selectedDate,
        donorName: _donorNameController.text,
        receiptNumber: receiptNumber,
        currentYear: _currentYearController.text,
      );

      setState(() {
        _status = pdfPath != null
            ? '✅ Fixed receipt generated successfully!\nPath: $pdfPath'
            : '✅ Fixed receipt opened in browser (Web platform)';
        _isGenerating = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Fixed मराठी पावती यशस्वीरित्या तयार झाली!'),
            backgroundColor: Colors.green,
          ),
        );
      }

    } catch (e) {
      setState(() {
        _status = '❌ Error generating fixed receipt: $e';
        _isGenerating = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Fixed मराठी पावती'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.orange.shade400, Colors.orange.shade600],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Column(
                  children: [
                    Text(
                      'Fixed मराठी पावती जनरेटर',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Hardcoded Labels - Unicode Devanagari Font',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // Fixed labels display
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(15),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '🔒 FIXED LABELS (Never Change):',
                      style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blue),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '• || श्री गणेश प्रसन्न ||\n'
                      '• गणेशोत्सव\n'
                      '• पावती नं.\n'
                      '• रोख मिळाले:\n'
                      '• अक्षरी:\n'
                      '• दिनांक:\n'
                      '• यांच्याकडून\n'
                      '• धन्यवाद...!\n'
                      '• सही\n'
                      '• रोख मिळाले...!\n'
                      '• Developed by AMSSoftX Web: https://amsssoftx.com',
                      style: TextStyle(fontSize: 12, color: Colors.blue),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              const Text(
                '📝 Dynamic Fields (Only these can be changed):',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),

              const SizedBox(height: 15),

              // Dynamic form fields
              Row(
                children: [
                  Expanded(
                    child: _buildTextField('पावती नं (Receipt No.)', _receiptNumberController, '1001', isNumber: true),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: _buildTextField('वर्ष (Year)', _currentYearController, '2025'),
                  ),
                ],
              ),

              // Date picker
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('दिनांक (Date)', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 5),
                  InkWell(
                    onTap: _selectDate,
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.calendar_today, size: 16),
                          const SizedBox(width: 8),
                          Text(DateFormat('dd/MM/yyyy').format(_selectedDate)),
                        ],
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 15),

              _buildTextField('देणगीदाराचे नाव (Donor Name)', _donorNameController, 'राम शर्मा'),
              _buildTextField('रक्कम (Amount)', _amountController, '4425', isNumber: true),

              const SizedBox(height: 20),

              // Status
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Text(_status, style: const TextStyle(fontSize: 14)),
              ),

              const SizedBox(height: 20),

              // Generate button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isGenerating ? null : _generateFixedReceipt,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    padding: const EdgeInsets.all(16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isGenerating
                      ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(color: Colors.white),
                            ),
                            SizedBox(width: 10),
                            Text('Fixed पावती तयार करत आहे...',
                                style: TextStyle(fontSize: 16, color: Colors.white)),
                          ],
                        )
                      : const Text(
                          'Fixed मराठी पावती तयार करा',
                          style: TextStyle(fontSize: 16, color: Colors.white),
                        ),
                ),
              ),

              const SizedBox(height: 20),

              // Preview of what will be generated
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.amber.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.amber.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '👁️ Preview (What will be generated):',
                      style: TextStyle(fontWeight: FontWeight.bold, color: Colors.amber),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${FixedMarathiReceiptGenerator.FIXED_TITLE}\n'
                      '${FixedMarathiReceiptGenerator.FIXED_FESTIVAL} ${_currentYearController.text}\n'
                      '${FixedMarathiReceiptGenerator.FIXED_RECEIPT_NO} ${_receiptNumberController.text}\n'
                      '${FixedMarathiReceiptGenerator.FIXED_DATE} ${DateFormat('dd/MM/yyyy').format(_selectedDate)}\n'
                      'सो.श्री ${_donorNameController.text} ${FixedMarathiReceiptGenerator.FIXED_FROM_SUFFIX}\n'
                      '${FixedMarathiReceiptGenerator.FIXED_CASH_RECEIVED} ₹${_amountController.text}\n'
                      '${FixedMarathiReceiptGenerator.FIXED_IN_WORDS} [Amount in Marathi words]\n'
                      '${FixedMarathiReceiptGenerator.FIXED_THANK_YOU}\n'
                      '${FixedMarathiReceiptGenerator.FIXED_SIGNATURE}',
                      style: const TextStyle(fontSize: 12, color: Colors.amber),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField(String label, TextEditingController controller, String hint,
      {bool isNumber = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 5),
          TextFormField(
            controller: controller,
            keyboardType: isNumber ? TextInputType.number : TextInputType.text,
            decoration: InputDecoration(
              hintText: hint,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.all(12),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter $label';
              }
              if (isNumber && double.tryParse(value) == null) {
                return 'Please enter a valid number';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }
}
