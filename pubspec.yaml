name: wargani
description: "A new Flutter project."
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9

  # Local Storage & Database
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.0.11
  sqflite: ^2.3.0

  # UI & Design
  cupertino_icons: ^1.0.8
  google_fonts: ^6.2.1
  flutter_animate: ^4.5.0
  shimmer: ^3.0.0
  cached_network_image: ^3.3.0

  # PDF & Documents
  pdf: ^3.11.3
  printing: ^5.9.0

  # Utilities
  intl: ^0.20.2
  image_picker: ^1.0.4
  share_plus: ^8.0.0
  url_launcher: ^6.3.2
  permission_handler: ^11.1.0
  shared_preferences: ^2.2.2

  # Security & Authentication
  local_auth: ^2.1.7
  crypto: ^3.0.3

  # Networking & API
  dio: ^5.4.0
  connectivity_plus: ^5.0.2

  # Analytics & Monitoring
  firebase_core: ^2.24.2
  firebase_analytics: ^10.7.4
  firebase_crashlytics: ^3.4.8

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.4.6

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/fonts/
    - assets/images/
  
  fonts:
    - family: Hind
      fonts:
        - asset: assets/fonts/Hind-Regular.ttf
