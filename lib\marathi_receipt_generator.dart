import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:intl/intl.dart';

/// Marathi Receipt PDF Generator with proper Unicode support
/// 
/// This class generates professional Marathi receipts (पावती) with:
/// - Perfect conjunct character rendering
/// - Embedded Unicode Devanagari fonts
/// - Amount in both digits and Marathi words
/// - Professional formatting

class MarathiReceiptGenerator {
  
  // Helper function to get proper Devanagari font
  static Future<pw.Font> _getDevanagariFont() async {
    try {
      // Primary: Noto Sans Devanagari - best for conjunct characters
      final font = await PdfGoogleFonts.notoSansDevanagariRegular();
      print('✅ Loaded Noto Sans Devanagari Regular font');
      return font;
    } catch (e1) {
      try {
        // Secondary: Noto Serif Devanagari
        final font = await PdfGoogleFonts.notoSerifDevanagariRegular();
        print('✅ Loaded Noto Serif Devanagari font');
        return font;
      } catch (e2) {
        try {
          // Fallback: Hind font from assets
          final fontData = await rootBundle.load("assets/fonts/Hind-Regular.ttf");
          final font = pw.Font.ttf(fontData);
          print('✅ Loaded Hind font from assets');
          return font;
        } catch (e3) {
          throw Exception('❌ No suitable Devanagari font found. Please ensure internet connectivity.');
        }
      }
    }
  }

  static Future<pw.Font> _getDevanagariBoldFont() async {
    try {
      // Primary: Noto Sans Devanagari Bold
      final font = await PdfGoogleFonts.notoSansDevanagariBold();
      print('✅ Loaded Noto Sans Devanagari Bold font');
      return font;
    } catch (e1) {
      try {
        // Fallback: Regular font for bold
        return await _getDevanagariFont();
      } catch (e2) {
        throw Exception('❌ No suitable bold Devanagari font found');
      }
    }
  }

  // Helper function to ensure proper Devanagari text rendering
  static String _ensureProperDevanagariText(String text) {
    if (text.isEmpty) return text;
    
    // Fix common conjunct characters for PDF rendering
    final Map<String, String> conjunctFixes = {
      'श्री': 'श्री',   // Shri
      'क्ष': 'क्ष',    // Ksha
      'त्र': 'त्र',    // Tra
      'ज्ञ': 'ज्ञ',    // Gya
      'द्व': 'द्व',    // Dva
      'स्व': 'स्व',    // Sva
      'प्र': 'प्र',    // Pra
      'स्थ': 'स्थ',    // Stha
      'न्त': 'न्त',    // Nta
      'न्द': 'न्द',    // Nda
      'स्त': 'स्त',    // Sta
      'न्य': 'न्य',    // Nya
      'त्य': 'त्य',    // Tya
      'द्य': 'द्य',    // Dya
      'म्प': 'म्प',    // Mpa
      'म्ब': 'म्ब',    // Mba
      'च्च': 'च्च',    // Cha
      'त्त': 'त्त',    // Tta
      'ल्ल': 'ल्ल',    // Lla
      'न्न': 'न्न',    // Nna
      'पू': 'पू',      // Poo
      'जन': 'जन',     // Jan
    };
    
    String processedText = text;
    conjunctFixes.forEach((original, fixed) {
      processedText = processedText.replaceAll(original, fixed);
    });
    
    return processedText.trim();
  }

  // Helper function to create Devanagari text widget
  static pw.Widget _createDevanagariText(
    String text,
    pw.Font font,
    double fontSize, {
    PdfColor? color,
    pw.FontWeight? fontWeight,
    pw.TextAlign? textAlign,
    bool isBold = false,
  }) {
    final processedText = _ensureProperDevanagariText(text);
    
    return pw.Text(
      processedText,
      style: pw.TextStyle(
        font: font,
        fontSize: fontSize,
        color: color ?? PdfColors.black,
        fontWeight: fontWeight ?? (isBold ? pw.FontWeight.bold : pw.FontWeight.normal),
        letterSpacing: 0.0, // No extra spacing for conjuncts
        height: 1.4, // Proper line height for Devanagari
      ),
      textAlign: textAlign ?? pw.TextAlign.left,
      textDirection: pw.TextDirection.ltr,
    );
  }

  // Convert number to Marathi words
  static String _convertToMarathiWords(double amount) {
    final int intAmount = amount.toInt();
    
    final Map<int, String> marathiNumbers = {
      0: 'शून्य', 1: 'एक', 2: 'दोन', 3: 'तीन', 4: 'चार', 5: 'पाच',
      6: 'सहा', 7: 'सात', 8: 'आठ', 9: 'नऊ', 10: 'दहा',
      11: 'अकरा', 12: 'बारा', 13: 'तेरा', 14: 'चौदा', 15: 'पंधरा',
      16: 'सोळा', 17: 'सतरा', 18: 'अठरा', 19: 'एकोणीस', 20: 'वीस',
      21: 'एकवीस', 22: 'बावीस', 23: 'तेवीस', 24: 'चोवीस', 25: 'पंचवीस',
      30: 'तीस', 40: 'चाळीस', 50: 'पन्नास', 60: 'साठ', 70: 'सत्तर',
      80: 'ऐंशी', 90: 'नव्वद', 100: 'शंभर'
    };
    
    if (intAmount == 0) return 'शून्य रुपये फक्त';
    
    String result = '';
    
    // Handle thousands
    if (intAmount >= 1000) {
      final thousands = intAmount ~/ 1000;
      if (thousands == 1) {
        result += 'एक हजार ';
      } else if (thousands <= 25 && marathiNumbers.containsKey(thousands)) {
        result += '${marathiNumbers[thousands]} हजार ';
      } else {
        result += '$thousands हजार ';
      }
    }
    
    // Handle hundreds
    final remainder = intAmount % 1000;
    if (remainder >= 100) {
      final hundreds = remainder ~/ 100;
      if (hundreds == 1) {
        result += 'एकशे ';
      } else if (marathiNumbers.containsKey(hundreds)) {
        result += '${marathiNumbers[hundreds]}शे ';
      } else {
        result += '${marathiNumbers[hundreds]}शे ';
      }
    }
    
    // Handle tens and units
    final tensAndUnits = remainder % 100;
    if (tensAndUnits > 0) {
      if (marathiNumbers.containsKey(tensAndUnits)) {
        result += marathiNumbers[tensAndUnits]!;
      } else if (tensAndUnits > 25) {
        final tens = tensAndUnits ~/ 10;
        final units = tensAndUnits % 10;
        
        if (tens == 3) {
          result += 'तीस';
        } else if (tens == 4) result += 'चाळीस';
        else if (tens == 5) result += 'पन्नास';
        else if (tens == 6) result += 'साठ';
        else if (tens == 7) result += 'सत्तर';
        else if (tens == 8) result += 'ऐंशी';
        else if (tens == 9) result += 'नव्वद';
        
        if (units > 0 && marathiNumbers.containsKey(units)) {
          result += marathiNumbers[units]!;
        }
      } else {
        result += tensAndUnits.toString();
      }
    }
    
    result += ' रुपये फक्त';
    return result.trim();
  }

  /// Generate Marathi receipt PDF
  static Future<String?> generateMarathiReceipt({
    required String title,
    required String organizationName,
    required String address,
    required int receiptNumber,
    required String donorName,
    required double amount,
    required DateTime date,
    String? mobileNumber,
    String? purpose,
  }) async {
    try {
      print('🧾 Starting Marathi receipt PDF generation...');
      
      // Load fonts
      final regularFont = await _getDevanagariFont();
      final boldFont = await _getDevanagariBoldFont();
      
      final pdf = pw.Document();
      
      // Convert amount to Marathi words
      final amountInWords = _convertToMarathiWords(amount);
      
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(30),
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header with title
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(20),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex("#FF8C00"), // Orange background
                    borderRadius: pw.BorderRadius.circular(10),
                  ),
                  child: pw.Column(
                    children: [
                      _createDevanagariText(
                        title,
                        boldFont,
                        20,
                        color: PdfColors.white,
                        isBold: true,
                        textAlign: pw.TextAlign.center,
                      ),
                      pw.SizedBox(height: 8),
                      _createDevanagariText(
                        organizationName,
                        regularFont,
                        14,
                        color: PdfColors.white,
                        textAlign: pw.TextAlign.center,
                      ),
                      if (address.isNotEmpty) ...[
                        pw.SizedBox(height: 4),
                        _createDevanagariText(
                          address,
                          regularFont,
                          12,
                          color: PdfColors.white,
                          textAlign: pw.TextAlign.center,
                        ),
                      ],
                    ],
                  ),
                ),
                
                pw.SizedBox(height: 30),
                
                // Receipt details
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    _createDevanagariText(
                      'पावती नं. $receiptNumber',
                      boldFont,
                      16,
                      isBold: true,
                    ),
                    _createDevanagariText(
                      'Date: ${DateFormat('dd/MM/yyyy').format(date)}',
                      regularFont,
                      14,
                    ),
                  ],
                ),
                
                pw.SizedBox(height: 25),
                
                // Donor name
                pw.Row(
                  children: [
                    _createDevanagariText(
                      'Mr/Mrs.',
                      regularFont,
                      14,
                    ),
                    pw.Expanded(
                      child: pw.Container(
                        decoration: const pw.BoxDecoration(
                          border: pw.Border(
                            bottom: pw.BorderSide(color: PdfColors.black),
                          ),
                        ),
                        child: pw.Text(
                          donorName,
                          style: pw.TextStyle(
                            font: boldFont,
                            fontSize: 14,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    _createDevanagariText(
                      ' यांच्याकडून',
                      regularFont,
                      14,
                    ),
                  ],
                ),
                
                pw.SizedBox(height: 20),
                
                // Mobile number (if provided)
                if (mobileNumber != null && mobileNumber.isNotEmpty) ...[
                  pw.Row(
                    children: [
                      _createDevanagariText(
                        'मोबाईल नं: ',
                        regularFont,
                        12,
                      ),
                      pw.Text(
                        mobileNumber,
                        style: pw.TextStyle(font: regularFont, fontSize: 12),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 15),
                ],
                
                // Amount section
                pw.Row(
                  children: [
                    _createDevanagariText(
                      'रोख: ',
                      regularFont,
                      14,
                    ),
                    pw.Container(
                      padding: const pw.EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                      decoration: pw.BoxDecoration(
                        border: pw.Border.all(color: PdfColors.black),
                        borderRadius: pw.BorderRadius.circular(5),
                      ),
                      child: pw.Text(
                        '₹ ${amount.toStringAsFixed(0)}',
                        style: pw.TextStyle(
                          font: boldFont,
                          fontSize: 16,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                
                pw.SizedBox(height: 20),
                
                // Amount in words
                pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    _createDevanagariText(
                      'अ.रु: ',
                      regularFont,
                      12,
                    ),
                    pw.Expanded(
                      child: pw.Container(
                        decoration: const pw.BoxDecoration(
                          border: pw.Border(
                            bottom: pw.BorderSide(color: PdfColors.black),
                          ),
                        ),
                        child: _createDevanagariText(
                          amountInWords,
                          regularFont,
                          12,
                        ),
                      ),
                    ),
                  ],
                ),
                
                pw.SizedBox(height: 20),
                
                // Purpose (if provided)
                if (purpose != null && purpose.isNotEmpty) ...[
                  pw.Row(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      _createDevanagariText(
                        'कारण: ',
                        regularFont,
                        12,
                      ),
                      pw.Expanded(
                        child: pw.Container(
                          decoration: const pw.BoxDecoration(
                            border: pw.Border(
                              bottom: pw.BorderSide(color: PdfColors.black),
                            ),
                          ),
                          child: _createDevanagariText(
                            purpose,
                            regularFont,
                            12,
                          ),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 20),
                ],
                
                pw.Spacer(),
                
                // Footer
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        _createDevanagariText(
                          'आपल्या दानाबद्दल आभारी आहोत!',
                          boldFont,
                          14,
                          isBold: true,
                        ),
                        pw.SizedBox(height: 15),
                        _createDevanagariText(
                          'सही',
                          regularFont,
                          12,
                        ),
                      ],
                    ),
                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.end,
                      children: [
                        _createDevanagariText(
                          'रोख...!',
                          boldFont,
                          14,
                          isBold: true,
                        ),
                      ],
                    ),
                  ],
                ),
                
                pw.SizedBox(height: 20),
                
                // Footer note
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex("#F0F8FF"),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Text(
                    'Generated with Unicode Devanagari font support',
                    style: pw.TextStyle(
                      font: regularFont,
                      fontSize: 8,
                      color: PdfColor.fromHex("#666666"),
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              ],
            );
          },
        ),
      );
      
      // Save PDF
      if (kIsWeb) {
        await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdf.save());
        return null;
      } else {
        final output = await getTemporaryDirectory();
        final file = File("${output.path}/marathi_receipt_${receiptNumber}_${DateTime.now().millisecondsSinceEpoch}.pdf");
        await file.writeAsBytes(await pdf.save());
        print('✅ Marathi receipt PDF generated: ${file.path}');
        return file.path;
      }
      
    } catch (e) {
      print('❌ Error generating Marathi receipt PDF: $e');
      throw Exception('Failed to generate Marathi receipt: $e');
    }
  }
}

/// Flutter Widget for Marathi Receipt Generator
class MarathiReceiptScreen extends StatefulWidget {
  const MarathiReceiptScreen({super.key});

  @override
  State<MarathiReceiptScreen> createState() => _MarathiReceiptScreenState();
}

class _MarathiReceiptScreenState extends State<MarathiReceiptScreen> {
  final _formKey = GlobalKey<FormState>();

  // Form controllers
  final _titleController = TextEditingController(text: '|| श्री गणेश पूजन ||');
  final _organizationController = TextEditingController(text: 'श्री गणेश मंडळ');
  final _addressController = TextEditingController(text: 'पुणे, महाराष्ट्र');
  final _receiptNumberController = TextEditingController(text: '1001');
  final _donorNameController = TextEditingController(text: 'राम शर्मा');
  final _amountController = TextEditingController(text: '4545');
  final _mobileController = TextEditingController(text: '9876543210');
  final _purposeController = TextEditingController(text: 'गणेश उत्सव दान');

  DateTime _selectedDate = DateTime.now();
  bool _isGenerating = false;
  String _status = 'Ready to generate Marathi receipt';

  @override
  void dispose() {
    _titleController.dispose();
    _organizationController.dispose();
    _addressController.dispose();
    _receiptNumberController.dispose();
    _donorNameController.dispose();
    _amountController.dispose();
    _mobileController.dispose();
    _purposeController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _generateReceipt() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isGenerating = true;
      _status = 'Generating Marathi receipt with Unicode fonts...';
    });

    try {
      final amount = double.parse(_amountController.text);
      final receiptNumber = int.parse(_receiptNumberController.text);

      final pdfPath = await MarathiReceiptGenerator.generateMarathiReceipt(
        title: _titleController.text,
        organizationName: _organizationController.text,
        address: _addressController.text,
        receiptNumber: receiptNumber,
        donorName: _donorNameController.text,
        amount: amount,
        date: _selectedDate,
        mobileNumber: _mobileController.text.isNotEmpty ? _mobileController.text : null,
        purpose: _purposeController.text.isNotEmpty ? _purposeController.text : null,
      );

      setState(() {
        _status = pdfPath != null
            ? '✅ Receipt generated successfully!\nPath: $pdfPath'
            : '✅ Receipt opened in browser (Web platform)';
        _isGenerating = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('मराठी पावती यशस्वीरित्या तयार झाली!'),
            backgroundColor: Colors.green,
          ),
        );
      }

    } catch (e) {
      setState(() {
        _status = '❌ Error generating receipt: $e';
        _isGenerating = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('मराठी पावती तयार करा'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.orange.shade400, Colors.orange.shade600],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Column(
                  children: [
                    Text(
                      'मराठी पावती जनरेटर',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Unicode Devanagari Font Support',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // Form fields
              _buildTextField('शीर्षक (Title)', _titleController, '|| श्री गणेश पूजन ||'),
              _buildTextField('संस्था नाव (Organization)', _organizationController, 'श्री गणेश मंडळ'),
              _buildTextField('पत्ता (Address)', _addressController, 'पुणे, महाराष्ट्र'),

              Row(
                children: [
                  Expanded(
                    child: _buildTextField('पावती नं (Receipt No.)', _receiptNumberController, '1001', isNumber: true),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Date (Date)', style: TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(height: 5),
                        InkWell(
                          onTap: _selectDate,
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.calendar_today, size: 16),
                                const SizedBox(width: 8),
                                Text(DateFormat('dd/MM/yyyy').format(_selectedDate)),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              _buildTextField('दानदाता नाव (Donor Name)', _donorNameController, 'राम शर्मा'),
              _buildTextField('रक्कम (Amount)', _amountController, '4545', isNumber: true),
              _buildTextField('मोबाईल नं (Mobile)', _mobileController, '9876543210', isOptional: true),
              _buildTextField('कारण (Purpose)', _purposeController, 'गणेश उत्सव दान', isOptional: true),

              const SizedBox(height: 20),

              // Status
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Text(_status, style: const TextStyle(fontSize: 14)),
              ),

              const SizedBox(height: 20),

              // Generate button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isGenerating ? null : _generateReceipt,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    padding: const EdgeInsets.all(16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isGenerating
                      ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(color: Colors.white),
                            ),
                            SizedBox(width: 10),
                            Text('पावती तयार करत आहे...',
                                style: TextStyle(fontSize: 16, color: Colors.white)),
                          ],
                        )
                      : const Text(
                          'मराठी पावती तयार करा',
                          style: TextStyle(fontSize: 16, color: Colors.white),
                        ),
                ),
              ),

              const SizedBox(height: 20),

              // Features note
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.amber.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.amber.shade200),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '✅ Features:',
                      style: TextStyle(fontWeight: FontWeight.bold, color: Colors.amber),
                    ),
                    SizedBox(height: 5),
                    Text(
                      '• Perfect conjunct character rendering (श्री, क्ष, त्र)\n'
                      '• Amount in Marathi words conversion\n'
                      '• Unicode Devanagari font embedded\n'
                      '• Professional receipt format\n'
                      '• Cross-platform PDF generation',
                      style: TextStyle(fontSize: 12, color: Colors.amber),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField(String label, TextEditingController controller, String hint,
      {bool isNumber = false, bool isOptional = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label + (isOptional ? ' (Optional)' : ''),
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 5),
          TextFormField(
            controller: controller,
            keyboardType: isNumber ? TextInputType.number : TextInputType.text,
            decoration: InputDecoration(
              hintText: hint,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.all(12),
            ),
            validator: isOptional ? null : (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter $label';
              }
              if (isNumber && double.tryParse(value) == null) {
                return 'Please enter a valid number';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }
}
