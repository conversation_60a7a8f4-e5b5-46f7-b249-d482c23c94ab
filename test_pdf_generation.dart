import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:wargani/utils/pdf_generator.dart';
import 'package:wargani/models/wargani_model.dart';
import 'package:wargani/models/profile_model.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:wargani/l10n/app_localizations.dart';

/// Test file to verify PDF generation with proper Marathi/Hindi text rendering
/// 
/// This test helps verify that:
/// 1. Devanagari fonts load correctly
/// 2. Marathi/Hindi text renders properly with correct spacing
/// 3. Unicode characters display correctly in PDF
/// 4. Text alignment and formatting works as expected

void main() {
  group('PDF Generation Tests', () {
    late Widget testApp;
    
    setUp(() async {
      // Initialize Hive for testing
      await HiveHelper.init();
      
      // Create test app with localization support
      testApp = const MaterialApp(
        localizationsDelegates: [
          AppLocalizations.delegate,
        ],
        supportedLocales: [
          Locale('en', ''),
          Locale('mr', ''), // Marathi
          Locale('hi', ''), // Hindi
        ],
        home: Scaffold(
          body: Text('Test App'),
        ),
      );
    });

    testWidgets('Test Marathi PDF Generation', (WidgetTester tester) async {
      await tester.pumpWidget(testApp);
      
      // Set locale to Marathi
      await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
        'flutter/localization',
        const StandardMethodCodec().encodeMethodCall(
          const MethodCall('routeUpdated', {
            'location': '/',
            'state': null,
            'routeName': '/',
          }),
        ),
        (data) {},
      );
      
      // Create test profile
      final profile = Profile(
        mandalName: 'श्री गणेश मंडळ',
        address: 'पुणे, महाराष्ट्र',
        currentYear: '2024',
        mandalRegistrationNo: 'MH/2024/001',
      );
      
      // Save profile to Hive
      final profileBox = HiveHelper.getProfileBox();
      await profileBox.add(profile);
      
      // Create test Wargani receipt
      final wargani = Wargani(
        receiptNo: 1,
        name: 'श्री राम शर्मा',
        amount: 1100.0,
        date: DateTime.now(),
        prefix: 'WR',
        mobileNo: '9876543210',
        amountInWords: 'एक हजार एकशे रुपये',
      );
      
      // Test PDF generation
      final context = tester.element(find.byType(Scaffold));
      
      try {
        final pdfPath = await PdfGenerator.generateProfessionalWarganiReceipt(
          context,
          wargani,
          'Test User',
        );
        
        // Verify PDF was generated
        expect(pdfPath, isNotNull);
        print('✅ Marathi PDF generated successfully: $pdfPath');
        
        // Test with Hindi locale
        final hindiWargani = Wargani(
          receiptNo: 2,
          name: 'श्री राम शर्मा',
          amount: 2100.0,
          date: DateTime.now(),
          prefix: 'WR',
          mobileNo: '9876543210',
          amountInWords: 'दो हजार एक सौ रुपये',
        );
        
        final hindiPdfPath = await PdfGenerator.generateProfessionalWarganiReceipt(
          context,
          hindiWargani,
          'Test User',
        );
        
        expect(hindiPdfPath, isNotNull);
        print('✅ Hindi PDF generated successfully: $hindiPdfPath');
        
      } catch (e) {
        print('❌ PDF Generation failed: $e');
        fail('PDF generation should not throw an exception');
      }
    });

    testWidgets('Test Font Loading', (WidgetTester tester) async {
      await tester.pumpWidget(testApp);
      
      // Test if Devanagari fonts can be loaded
      try {
        // This will test the font loading methods we created
        print('Testing font loading...');
        
        // The font loading will be tested when PDF generation is called
        // as the fonts are loaded asynchronously during PDF creation
        
        print('✅ Font loading test setup complete');
      } catch (e) {
        print('❌ Font loading test failed: $e');
        fail('Font loading should not fail');
      }
    });
  });
}

/// Helper function to test specific Devanagari text rendering
void testDevanagariText() {
  final testTexts = [
    'श्री गणेश प्रसन्न',
    'पावती नं.',
    'दिनांक:',
    'सो.श्री',
    'यांच्याकडून',
    'रोख मिळाले:',
    'अक्षरी:',
    'धन्यवाद...!',
    'सही',
    'गणेशोत्सव',
  ];
  
  print('Testing Devanagari text rendering:');
  for (final text in testTexts) {
    print('✓ $text');
  }
}

/// Instructions for manual testing:
/// 
/// 1. Run this test file: `flutter test test_pdf_generation.dart`
/// 2. Check the generated PDF files in the temporary directory
/// 3. Verify that Marathi/Hindi text appears correctly
/// 4. Check for proper spacing and alignment
/// 5. Ensure conjunct characters render properly
/// 
/// Common issues to look for:
/// - Missing or broken Devanagari characters
/// - Incorrect spacing between characters
/// - Misaligned text
/// - Font fallback to default fonts
/// 
/// If issues persist:
/// 1. Ensure Noto Sans Devanagari fonts are available
/// 2. Check internet connectivity for Google Fonts
/// 3. Verify Hind font is properly loaded from assets
/// 4. Test on different devices/platforms
